package hosts

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"runtime"
	"strings"
	"sync"
)

const (
	// 标记注释，用于识别我们添加的条目
	markerComment = "# Added by go-augment-intercept-report"
)

// Manager hosts文件管理器
type Manager struct {
	mu       sync.RWMutex
	filePath string
	entries  map[string]string // domain -> ip
}

// NewManager 创建新的hosts管理器
func NewManager() *Manager {
	return &Manager{
		filePath: getHostsFilePath(),
		entries:  make(map[string]string),
	}
}

// getHostsFilePath 获取hosts文件路径
func getHostsFilePath() string {
	switch runtime.GOOS {
	case "windows":
		return `C:\Windows\System32\drivers\etc\hosts`
	case "darwin", "linux":
		return "/etc/hosts"
	default:
		return "/etc/hosts"
	}
}

// AddEntry 添加hosts条目
func (m *Manager) AddEntry(domain, ip string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 检查是否已存在
	if existingIP, exists := m.entries[domain]; exists && existingIP == ip {
		return nil // 已存在相同条目
	}

	// 读取现有文件内容
	content, err := m.readHostsFile()
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	// 移除已存在的条目（如果有）
	content = m.removeExistingEntry(content, domain)

	// 添加新条目
	newEntry := fmt.Sprintf("%s\t%s\t%s", ip, domain, markerComment)
	content = append(content, newEntry)

	// 写入文件
	if err := m.writeHostsFile(content); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	// 更新内存记录
	m.entries[domain] = ip
	return nil
}

// RemoveEntry 移除hosts条目
func (m *Manager) RemoveEntry(domain string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 检查是否存在
	if _, exists := m.entries[domain]; !exists {
		return nil // 不存在，无需移除
	}

	// 读取现有文件内容
	content, err := m.readHostsFile()
	if err != nil {
		return fmt.Errorf("读取hosts文件失败: %w", err)
	}

	// 移除条目
	content = m.removeExistingEntry(content, domain)

	// 写入文件
	if err := m.writeHostsFile(content); err != nil {
		return fmt.Errorf("写入hosts文件失败: %w", err)
	}

	// 更新内存记录
	delete(m.entries, domain)
	return nil
}

// readHostsFile 读取hosts文件内容
func (m *Manager) readHostsFile() ([]string, error) {
	file, err := os.Open(m.filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		lines = append(lines, scanner.Text())
	}

	return lines, scanner.Err()
}

// writeHostsFile 写入hosts文件内容
func (m *Manager) writeHostsFile(lines []string) error {
	// 创建临时文件
	tmpFile, err := os.CreateTemp("", "hosts_tmp_*")
	if err != nil {
		return err
	}
	defer os.Remove(tmpFile.Name())

	// 写入内容
	writer := bufio.NewWriter(tmpFile)
	for _, line := range lines {
		if _, err := writer.WriteString(line + "\n"); err != nil {
			tmpFile.Close()
			return err
		}
	}

	if err := writer.Flush(); err != nil {
		tmpFile.Close()
		return err
	}
	tmpFile.Close()

	// 原子性替换原文件
	return m.atomicReplace(tmpFile.Name(), m.filePath)
}

// removeExistingEntry 从内容中移除指定域名的条目
func (m *Manager) removeExistingEntry(lines []string, domain string) []string {
	var result []string
	for _, line := range lines {
		// 跳过我们添加的包含该域名的条目
		if strings.Contains(line, markerComment) && strings.Contains(line, domain) {
			continue
		}
		result = append(result, line)
	}
	return result
}

// atomicReplace 原子性替换文件
func (m *Manager) atomicReplace(src, dst string) error {
	// 复制文件内容
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.OpenFile(dst, os.O_WRONLY|os.O_TRUNC, 0644)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = io.Copy(dstFile, srcFile)
	return err
}

// ListEntries 列出当前管理的所有条目
func (m *Manager) ListEntries() map[string]string {
	m.mu.RLock()
	defer m.mu.RUnlock()

	result := make(map[string]string)
	for domain, ip := range m.entries {
		result[domain] = ip
	}
	return result
}

// IsManaged 检查域名是否被管理
func (m *Manager) IsManaged(domain string) bool {
	m.mu.RLock()
	defer m.mu.RUnlock()

	_, exists := m.entries[domain]
	return exists
}
