package cli

import (
	"fmt"
	"os"

	"go-augment-intercept-report/internal/config"
	"go-augment-intercept-report/internal/proxy"
	"go-augment-intercept-report/pkg/hosts"
	"go-augment-intercept-report/pkg/logger"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
)

var (
	cfgFile string
	cfg     *config.Config
)

// rootCmd 代表没有调用子命令时的基础命令
var rootCmd = &cobra.Command{
	Use:   "go-augment-intercept-report",
	Short: "Go语言本地HTTPS拦截代理系统",
	Long: `一个基于Go语言的本地HTTPS拦截代理系统，具备以下功能：
- DNS拦截与重定向（自动配置hosts文件）
- HTTPS代理服务（自动生成SSL证书）
- 请求拦截与处理（特定URL路径返回假数据）
- 多域名支持（支持多个子域名的分别处理）`,
	RunE: runProxy,
}

// Execute 添加所有子命令到根命令并设置适当的标志
func Execute() error {
	return rootCmd.Execute()
}

func init() {
	cobra.OnInitialize(initConfig)

	// 全局标志
	rootCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "配置文件路径 (默认为 $HOME/.go-augment-intercept-report.yaml)")

	// 代理相关标志
	rootCmd.Flags().StringP("addr", "a", ":9080", "代理监听地址")
	rootCmd.Flags().StringP("web-addr", "w", ":9081", "Web界面监听地址")
	rootCmd.Flags().StringSliceP("domains", "d", []string{}, "要拦截的域名列表 (例如: 1.a.com,2.a.com)")
	rootCmd.Flags().StringSliceP("intercept-paths", "p", []string{"/client-metrics"}, "要拦截的路径列表")
	rootCmd.Flags().StringP("fake-response", "r", "{}", "拦截路径返回的假数据")
	rootCmd.Flags().BoolP("auto-hosts", "H", true, "自动管理hosts文件")
	rootCmd.Flags().BoolP("debug", "D", false, "启用调试模式")

	// 绑定标志到viper
	viper.BindPFlag("proxy.addr", rootCmd.Flags().Lookup("addr"))
	viper.BindPFlag("proxy.web_addr", rootCmd.Flags().Lookup("web-addr"))
	viper.BindPFlag("proxy.domains", rootCmd.Flags().Lookup("domains"))
	viper.BindPFlag("proxy.intercept_paths", rootCmd.Flags().Lookup("intercept-paths"))
	viper.BindPFlag("proxy.fake_response", rootCmd.Flags().Lookup("fake-response"))
	viper.BindPFlag("proxy.auto_hosts", rootCmd.Flags().Lookup("auto-hosts"))
	viper.BindPFlag("debug", rootCmd.Flags().Lookup("debug"))
}

// initConfig 读取配置文件和环境变量
func initConfig() {
	if cfgFile != "" {
		viper.SetConfigFile(cfgFile)
	} else {
		home, err := os.UserHomeDir()
		cobra.CheckErr(err)

		viper.AddConfigPath(home)
		viper.AddConfigPath(".")
		viper.SetConfigType("yaml")
		viper.SetConfigName(".go-augment-intercept-report")
	}

	viper.AutomaticEnv()

	if err := viper.ReadInConfig(); err == nil {
		fmt.Fprintln(os.Stderr, "使用配置文件:", viper.ConfigFileUsed())
	}

	// 解析配置
	cfg = config.DefaultConfig()
	if err := viper.Unmarshal(cfg); err != nil {
		fmt.Fprintf(os.Stderr, "解析配置失败: %v\n", err)
		os.Exit(1)
	}
}

// runProxy 启动代理服务
func runProxy(cmd *cobra.Command, args []string) error {
	// 初始化日志
	logger.Init(cfg.Debug)
	log := logger.GetLogger()

	log.Info("启动 Go Augment Intercept Report 代理系统")
	log.Infof("配置: %+v", cfg)

	// 管理hosts文件
	if cfg.Proxy.AutoHosts && len(cfg.Proxy.Domains) > 0 {
		hostsManager := hosts.NewManager()

		log.Info("配置hosts文件...")
		for _, domain := range cfg.Proxy.Domains {
			if err := hostsManager.AddEntry(domain, "127.0.0.1"); err != nil {
				log.Errorf("添加hosts条目失败 %s: %v", domain, err)
				return err
			}
			log.Infof("已添加hosts条目: %s -> 127.0.0.1", domain)
		}

		// 设置清理函数
		defer func() {
			log.Info("清理hosts文件...")
			for _, domain := range cfg.Proxy.Domains {
				if err := hostsManager.RemoveEntry(domain); err != nil {
					log.Errorf("移除hosts条目失败 %s: %v", domain, err)
				}
			}
		}()
	}

	// 启动代理服务
	proxyServer := proxy.NewServer(cfg)
	return proxyServer.Start()
}
