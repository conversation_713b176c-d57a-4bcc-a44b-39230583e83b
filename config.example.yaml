# Go Augment Intercept Report 配置文件示例

# 调试模式
debug: false

# 代理配置
proxy:
  # 代理监听地址
  addr: ":9080"
  
  # Web界面监听地址
  web_addr: ":9081"
  
  # 要拦截的域名列表
  domains:
    - "1.a.com"
    - "2.a.com"
    - "3.a.com"
    - "4.a.com"
    - "5.a.com"
    - "6.a.com"
    - "7.a.com"
    - "8.a.com"
    - "9.a.com"
    - "10.a.com"
    - "11.a.com"
    - "12.a.com"
    - "13.a.com"
    - "14.a.com"
    - "15.a.com"
    - "16.a.com"
    - "17.a.com"
    - "18.a.com"
    - "19.a.com"
    - "20.a.com"
  
  # 要拦截的路径列表
  intercept_paths:
    - "/client-metrics"
    - "/api/metrics"
    - "/analytics"
  
  # 拦截路径返回的假数据
  fake_response: "{}"
  
  # 是否自动管理hosts文件
  auto_hosts: true
  
  # 证书存储路径（留空使用默认路径 ~/.mitmproxy）
  cert_path: ""
  
  # 上游代理（可选）
  upstream: ""
  
  # 是否忽略上游SSL证书验证
  ssl_insecure: false
