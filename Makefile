# Go Augment Intercept Report Makefile

# 变量定义
BINARY_NAME=go-augment-intercept-report
MAIN_PATH=cmd/main.go
BUILD_DIR=build
VERSION=$(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
LDFLAGS=-ldflags "-X main.version=$(VERSION)"

# 默认目标
.PHONY: all
all: clean deps build

# 安装依赖
.PHONY: deps
deps:
	@echo "正在安装依赖..."
	go mod tidy
	go mod download

# 编译
.PHONY: build
build:
	@echo "正在编译..."
	@mkdir -p $(BUILD_DIR)
	go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) $(MAIN_PATH)

# 跨平台编译
.PHONY: build-all
build-all: clean deps
	@echo "正在进行跨平台编译..."
	@mkdir -p $(BUILD_DIR)
	
	# Windows amd64
	GOOS=windows GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-windows-amd64.exe $(MAIN_PATH)
	
	# macOS amd64
	GOOS=darwin GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-amd64 $(MAIN_PATH)
	
	# macOS arm64
	GOOS=darwin GOARCH=arm64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-darwin-arm64 $(MAIN_PATH)
	
	# Linux amd64
	GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(BINARY_NAME)-linux-amd64 $(MAIN_PATH)

# 运行
.PHONY: run
run: build
	@echo "正在启动代理服务..."
	./$(BUILD_DIR)/$(BINARY_NAME) --debug

# 运行示例配置
.PHONY: run-example
run-example: build
	@echo "正在使用示例配置启动..."
	./$(BUILD_DIR)/$(BINARY_NAME) --config config.example.yaml --debug

# 清理
.PHONY: clean
clean:
	@echo "正在清理..."
	@rm -rf $(BUILD_DIR)
	@go clean

# 格式化代码
.PHONY: fmt
fmt:
	@echo "正在格式化代码..."
	go fmt ./...

# 代码检查
.PHONY: lint
lint:
	@echo "正在进行代码检查..."
	golangci-lint run

# 安装到系统
.PHONY: install
install: build
	@echo "正在安装到系统..."
	@cp $(BUILD_DIR)/$(BINARY_NAME) /usr/local/bin/

# 卸载
.PHONY: uninstall
uninstall:
	@echo "正在从系统卸载..."
	@rm -f /usr/local/bin/$(BINARY_NAME)

# 显示帮助
.PHONY: help
help:
	@echo "可用的make目标:"
	@echo "  all         - 清理、安装依赖并编译"
	@echo "  deps        - 安装Go依赖"
	@echo "  build       - 编译二进制文件"
	@echo "  build-all   - 跨平台编译"
	@echo "  run         - 编译并运行（调试模式）"
	@echo "  run-example - 使用示例配置运行"
	@echo "  clean       - 清理构建文件"
	@echo "  fmt         - 格式化代码"
	@echo "  lint        - 代码检查"
	@echo "  install     - 安装到系统"
	@echo "  uninstall   - 从系统卸载"
	@echo "  help        - 显示此帮助信息"
