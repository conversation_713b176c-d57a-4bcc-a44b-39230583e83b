package proxy

import (
	"context"
	"fmt"
	"os"
	"os/signal"
	"syscall"
	"time"

	"go-augment-intercept-report/internal/config"
	"go-augment-intercept-report/internal/proxy/addons"
	"go-augment-intercept-report/pkg/logger"

	"github.com/lqqyt2423/go-mitmproxy/proxy"
	"github.com/sirupsen/logrus"
)

// Server 代理服务器
type Server struct {
	config *config.Config
	proxy  *proxy.Proxy
	log    *logrus.Logger
}

// NewServer 创建新的代理服务器
func NewServer(cfg *config.Config) *Server {
	return &Server{
		config: cfg,
		log:    logger.GetLogger(),
	}
}

// Start 启动代理服务器
func (s *Server) Start() error {
	s.log.Info("正在启动代理服务器...")

	// 配置代理选项
	opts := &proxy.Options{
		Addr:              s.config.Proxy.Addr,
		StreamLargeBodies: 1024 * 1024 * 5, // 5MB
		SslInsecure:       s.config.Proxy.SSLInsecure,
		CaRootPath:        s.config.Proxy.CertPath,
		Upstream:          s.config.Proxy.Upstream,
	}

	// 创建代理实例
	p, err := proxy.NewProxy(opts)
	if err != nil {
		return fmt.Errorf("创建代理失败: %w", err)
	}
	s.proxy = p

	// 添加自定义插件
	interceptor := addons.NewInterceptor(&addons.InterceptorConfig{
		Domains:        s.config.Proxy.Domains,
		InterceptPaths: s.config.Proxy.InterceptPaths,
		FakeResponse:   s.config.Proxy.FakeResponse,
		Debug:          s.config.Debug,
	})
	p.AddAddon(interceptor)

	// 启动代理服务
	go func() {
		s.log.Infof("代理服务器启动在: %s", s.config.Proxy.Addr)
		s.log.Infof("Web界面地址: http://localhost%s", s.config.Proxy.WebAddr)
		if err := p.Start(); err != nil {
			s.log.Fatalf("代理服务器启动失败: %v", err)
		}
	}()

	// 等待中断信号
	return s.waitForShutdown()
}

// waitForShutdown 等待关闭信号
func (s *Server) waitForShutdown() error {
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	<-quit
	s.log.Info("正在关闭代理服务器...")

	// 创建超时上下文
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优雅关闭
	if s.proxy != nil {
		// go-mitmproxy 没有提供优雅关闭方法，这里只是示例
		s.log.Info("代理服务器已关闭")
	}

	select {
	case <-ctx.Done():
		s.log.Warn("关闭超时，强制退出")
		return ctx.Err()
	default:
		s.log.Info("代理服务器已优雅关闭")
		return nil
	}
}
