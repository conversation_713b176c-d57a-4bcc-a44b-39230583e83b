# 系统架构设计

## 整体架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   客户端应用     │    │   本地代理系统    │    │   目标服务器     │
│                │    │                  │    │                │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │  ┌───────────┐  │
│  │ 浏览器/APP │  │    │  │ HTTPS代理   │ │    │  │ 原始服务   │  │
│  └───────────┘  │    │  │ (9080)      │ │    │  └───────────┘  │
│        │        │    │  └─────────────┘ │    │        │        │
│        │        │    │         │        │    │        │        │
│  ┌───────────┐  │    │  ┌─────────────┐ │    │        │        │
│  │ DNS解析   │  │    │  │ 拦截插件    │ │    │        │        │
│  │ (hosts)   │  │    │  └─────────────┘ │    │        │        │
│  └───────────┘  │    │         │        │    │        │        │
│        │        │    │  ┌─────────────┐ │    │        │        │
│        │        │    │  │ Web界面     │ │    │        │        │
│        │        │    │  │ (9081)      │ │    │        │        │
│        │        │    │  └─────────────┘ │    │        │        │
└─────────────────┘    └──────────────────┘    └─────────────────┘
         │                        │                        │
         └────────────────────────┼────────────────────────┘
                                  │
                            ┌─────────────┐
                            │ hosts文件   │
                            │ 管理器      │
                            └─────────────┘
```

## 核心模块

### 1. CLI模块 (`internal/cli/`)
- **职责**: 命令行接口、配置解析、应用启动
- **主要组件**:
  - `root.go`: 根命令和参数定义
  - 配置文件解析和验证
  - 优雅启动和关闭

### 2. 配置模块 (`internal/config/`)
- **职责**: 配置结构定义和默认值管理
- **主要组件**:
  - `Config`: 应用程序配置结构
  - `ProxyConfig`: 代理相关配置
  - 支持YAML配置文件和命令行参数

### 3. 代理模块 (`internal/proxy/`)
- **职责**: 核心代理服务器和插件管理
- **主要组件**:
  - `Server`: 代理服务器封装
  - `addons/Interceptor`: 自定义拦截插件
  - 基于go-mitmproxy的HTTPS中间人代理

### 4. Hosts管理模块 (`pkg/hosts/`)
- **职责**: 跨平台hosts文件管理
- **主要组件**:
  - `Manager`: hosts文件管理器
  - 原子性文件操作
  - 自动备份和恢复

### 5. 日志模块 (`pkg/logger/`)
- **职责**: 统一日志管理
- **主要组件**:
  - 基于logrus的结构化日志
  - 调试模式支持
  - 彩色输出

## 数据流设计

### 请求处理流程

```
1. 客户端发起HTTPS请求 (https://1.a.com/client-metrics)
   ↓
2. DNS解析 (hosts文件) → 127.0.0.1
   ↓
3. 请求到达本地代理 (127.0.0.1:9080)
   ↓
4. TLS握手 (使用自签名证书)
   ↓
5. 解密HTTPS流量
   ↓
6. 拦截插件处理:
   ├─ 检查域名是否匹配
   ├─ 检查路径是否需要拦截
   └─ 决定返回假数据或转发
   ↓
7a. 拦截路径 → 返回假数据 (JSON)
   ↓
8a. 响应客户端
   
7b. 非拦截路径 → 转发到原始服务器
   ↓
8b. 获取真实响应
   ↓
9b. 响应客户端
```

### 配置管理流程

```
1. 命令行参数解析
   ↓
2. 配置文件读取 (可选)
   ↓
3. 参数合并和验证
   ↓
4. 配置对象创建
   ↓
5. 各模块初始化
```

### Hosts文件管理流程

```
1. 启动时备份原始hosts文件
   ↓
2. 添加域名映射条目
   ↓
3. 原子性写入hosts文件
   ↓
4. 运行期间监控状态
   ↓
5. 关闭时清理添加的条目
   ↓
6. 恢复原始hosts文件
```

## 插件架构

### Addon接口实现

```go
type Interceptor struct {
    proxy.BaseAddon
    config *InterceptorConfig
    log    *logrus.Logger
}

// 关键事件钩子
func (i *Interceptor) Request(f *proxy.Flow)     // 请求拦截
func (i *Interceptor) Response(f *proxy.Flow)    // 响应修改
func (i *Interceptor) Requestheaders(f *proxy.Flow)  // 请求头处理
func (i *Interceptor) Responseheaders(f *proxy.Flow) // 响应头处理
```

### 拦截逻辑

1. **域名匹配**: 检查请求域名是否在配置的拦截列表中
2. **路径匹配**: 检查请求路径是否匹配拦截规则
3. **标记机制**: 在Request阶段标记需要拦截的请求
4. **响应替换**: 在Response阶段替换响应内容

## 安全考虑

### 证书管理
- 使用go-mitmproxy兼容的证书存储
- 自动生成根证书和域名证书
- 证书存储在用户目录 `~/.mitmproxy/`

### 权限管理
- hosts文件修改需要管理员权限
- 自动检测权限状态
- 提供权限不足时的降级方案

### 数据安全
- 仅拦截指定域名和路径
- 不记录敏感数据
- 支持调试模式的详细日志控制

## 扩展性设计

### 插件扩展
- 基于go-mitmproxy的Addon接口
- 支持多个插件同时运行
- 事件驱动的处理模型

### 配置扩展
- 支持YAML配置文件
- 支持环境变量覆盖
- 支持运行时配置更新

### 平台扩展
- 跨平台hosts文件管理
- 平台特定的权限检查
- 统一的API接口
