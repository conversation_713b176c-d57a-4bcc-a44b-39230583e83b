package config

// Config 应用程序配置结构
type Config struct {
	Debug bool        `mapstructure:"debug" yaml:"debug"`
	Proxy ProxyConfig `mapstructure:"proxy" yaml:"proxy"`
}

// ProxyConfig 代理相关配置
type ProxyConfig struct {
	// 代理监听地址
	Addr string `mapstructure:"addr" yaml:"addr"`
	
	// Web界面监听地址
	WebAddr string `mapstructure:"web_addr" yaml:"web_addr"`
	
	// 要拦截的域名列表
	Domains []string `mapstructure:"domains" yaml:"domains"`
	
	// 要拦截的路径列表
	InterceptPaths []string `mapstructure:"intercept_paths" yaml:"intercept_paths"`
	
	// 拦截路径返回的假数据
	FakeResponse string `mapstructure:"fake_response" yaml:"fake_response"`
	
	// 是否自动管理hosts文件
	AutoHosts bool `mapstructure:"auto_hosts" yaml:"auto_hosts"`
	
	// 证书存储路径
	CertPath string `mapstructure:"cert_path" yaml:"cert_path"`
	
	// 上游代理
	Upstream string `mapstructure:"upstream" yaml:"upstream"`
	
	// 是否忽略上游SSL证书验证
	SSLInsecure bool `mapstructure:"ssl_insecure" yaml:"ssl_insecure"`
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Debug: false,
		Proxy: ProxyConfig{
			Addr:           ":9080",
			WebAddr:        ":9081",
			Domains:        []string{},
			InterceptPaths: []string{"/client-metrics"},
			FakeResponse:   "{}",
			AutoHosts:      true,
			CertPath:       "",
			Upstream:       "",
			SSLInsecure:    false,
		},
	}
}
