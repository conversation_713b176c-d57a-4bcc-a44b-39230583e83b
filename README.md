# Go Augment Intercept Report

基于Go语言的本地HTTPS拦截代理系统，支持DNS拦截、HTTPS代理和请求拦截处理。

## 核心功能

### 🔧 DNS拦截与重定向
- 自动配置本地hosts文件，将目标域名重定向到本地服务
- 支持多个子域名同时管理（如 1.a.com, 2.a.com, ... 20.a.com）
- 支持一键恢复原始DNS配置

### 🔒 HTTPS代理服务
- 启动本地HTTPS服务器作为代理（默认端口9080）
- 自动生成和管理SSL证书（兼容mitmproxy证书）
- 内置Web调试界面（默认端口9081）

### 🎯 请求拦截与处理
- 拦截特定URL路径（如 `/client-metrics`）
- 对拦截的请求返回预定义的假数据
- 其他请求透明转发到原始目标服务器

### 🌐 多域名支持
- 支持多个子域名的分别处理
- 基于域名和路径的精确路由规则
- 支持用户会话隔离

## 快速开始

### 安装依赖

```bash
go mod tidy
```

### 编译

```bash
go build -o go-augment-intercept-report cmd/main.go
```

### 基本使用

```bash
# 使用默认配置启动
./go-augment-intercept-report

# 指定要拦截的域名
./go-augment-intercept-report -d "1.a.com,2.a.com,3.a.com"

# 指定拦截路径和假数据
./go-augment-intercept-report -p "/client-metrics,/api/metrics" -r '{"status":"ok"}'

# 使用配置文件
./go-augment-intercept-report --config config.yaml
```

### 配置文件

复制示例配置文件并修改：

```bash
cp config.example.yaml config.yaml
```

## 使用流程

### 1. 启动代理服务

```bash
./go-augment-intercept-report -d "1.a.com,2.a.com" -p "/client-metrics"
```

### 2. 安装证书

首次启动后，需要安装根证书以支持HTTPS拦截：

- **Windows**: 证书位于 `%USERPROFILE%\.mitmproxy\mitmproxy-ca-cert.pem`
- **macOS/Linux**: 证书位于 `~/.mitmproxy/mitmproxy-ca-cert.pem`

将证书安装到系统信任存储中。

### 3. 配置客户端代理

在需要拦截的应用程序中配置HTTP代理：
- 代理地址：`127.0.0.1:9080`
- 支持HTTP和HTTPS

### 4. 访问Web界面

打开浏览器访问 `http://localhost:9081` 查看实时流量。

## 命令行参数

```
Usage:
  go-augment-intercept-report [flags]

Flags:
  -a, --addr string              代理监听地址 (default ":9080")
      --config string            配置文件路径
  -D, --debug                    启用调试模式
  -d, --domains strings          要拦截的域名列表
  -r, --fake-response string     拦截路径返回的假数据 (default "{}")
  -h, --help                     help for go-augment-intercept-report
  -H, --auto-hosts               自动管理hosts文件 (default true)
  -p, --intercept-paths strings  要拦截的路径列表 (default [/client-metrics])
  -w, --web-addr string          Web界面监听地址 (default ":9081")
```

## 技术架构

### 核心组件

- **CLI模块**: 命令行接口和配置管理
- **Proxy模块**: 基于go-mitmproxy的代理服务器
- **Hosts模块**: 跨平台hosts文件管理
- **Addons模块**: 自定义拦截插件

### 数据流

```
客户端请求 → DNS解析(hosts) → 本地代理 → 路径检查 → 拦截/转发 → 响应
```

## 开发

### 项目结构

```
├── cmd/                    # 主程序入口
├── internal/
│   ├── cli/               # 命令行接口
│   ├── config/            # 配置管理
│   └── proxy/             # 代理服务
│       └── addons/        # 自定义插件
├── pkg/
│   ├── hosts/             # hosts文件管理
│   └── logger/            # 日志管理
├── config.example.yaml    # 配置文件示例
└── README.md
```

### 添加自定义拦截逻辑

修改 `internal/proxy/addons/interceptor.go` 文件中的拦截逻辑：

```go
func (i *Interceptor) Response(f *proxy.Flow) {
    // 自定义拦截逻辑
    if i.shouldIntercept(f.Request.URL.Path) {
        // 返回自定义响应
        f.Response.Body = []byte(`{"custom": "response"}`)
    }
}
```

## 注意事项

1. **管理员权限**: 修改hosts文件需要管理员权限
2. **证书安装**: 首次使用需要手动安装根证书
3. **代理配置**: 客户端需要配置HTTP代理设置
4. **防火墙**: 确保代理端口未被防火墙阻止

## 许可证

MIT License
