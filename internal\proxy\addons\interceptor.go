package addons

import (
	"io"
	"strings"

	"go-augment-intercept-report/pkg/logger"

	"github.com/lqqyt2423/go-mitmproxy/proxy"
	"github.com/sirupsen/logrus"
)

// InterceptorConfig 拦截器配置
type InterceptorConfig struct {
	// 要拦截的域名列表
	Domains []string

	// 要拦截的路径列表
	InterceptPaths []string

	// 拦截路径返回的假数据
	FakeResponse string

	// 调试模式
	Debug bool
}

// Interceptor 请求拦截器插件
type Interceptor struct {
	proxy.BaseAddon
	config *InterceptorConfig
	log    *logrus.Logger
}

// NewInterceptor 创建新的拦截器
func NewInterceptor(config *InterceptorConfig) *Interceptor {
	return &Interceptor{
		BaseAddon: proxy.BaseAddon{},
		config:    config,
		log:       logger.GetLogger(),
	}
}

// ClientConnected 客户端连接事件
func (i *Interceptor) ClientConnected(clientConn *proxy.ClientConn) {
	if i.config.Debug {
		i.log.Debugf("客户端连接: %s", clientConn.Conn.RemoteAddr())
	}
}

// ClientDisconnected 客户端断开连接事件
func (i *Interceptor) ClientDisconnected(clientConn *proxy.ClientConn) {
	if i.config.Debug {
		i.log.Debugf("客户端断开连接: %s", clientConn.Conn.RemoteAddr())
	}
}

// ServerConnected 服务器连接事件
func (i *Interceptor) ServerConnected(connCtx *proxy.ConnContext) {
	if i.config.Debug {
		i.log.Debugf("服务器连接: %s", connCtx.ServerConn.Address)
	}
}

// ServerDisconnected 服务器断开连接事件
func (i *Interceptor) ServerDisconnected(connCtx *proxy.ConnContext) {
	if i.config.Debug {
		i.log.Debugf("服务器断开连接: %s", connCtx.ServerConn.Address)
	}
}

// TlsEstablishedServer TLS握手完成事件
func (i *Interceptor) TlsEstablishedServer(connCtx *proxy.ConnContext) {
	if i.config.Debug {
		i.log.Debugf("TLS握手完成: %s", connCtx.ServerConn.Address)
	}
}

// Requestheaders 请求头事件
func (i *Interceptor) Requestheaders(f *proxy.Flow) {
	if i.config.Debug {
		i.log.Debugf("请求头: %s %s", f.Request.Method, f.Request.URL.String())
	}
}

// Request 完整请求事件
func (i *Interceptor) Request(f *proxy.Flow) {
	host := f.Request.URL.Host
	path := f.Request.URL.Path

	i.log.Infof("请求: %s %s%s", f.Request.Method, host, path)

	// 检查是否为目标域名
	if !i.isTargetDomain(host) {
		if i.config.Debug {
			i.log.Debugf("非目标域名，跳过: %s", host)
		}
		return
	}

	// 检查是否为拦截路径
	if i.shouldIntercept(path) {
		i.log.Infof("拦截请求: %s%s", host, path)
		// 标记为需要拦截，在Response阶段处理
		f.Request.Header.Set("X-Intercept", "true")
	}
}

// Responseheaders 响应头事件
func (i *Interceptor) Responseheaders(f *proxy.Flow) {
	if i.config.Debug {
		i.log.Debugf("响应头: %d %s", f.Response.StatusCode, f.Request.URL.String())
	}
}

// Response 完整响应事件
func (i *Interceptor) Response(f *proxy.Flow) {
	// 检查是否需要拦截
	if f.Request.Header.Get("X-Intercept") == "true" {
		i.log.Infof("返回假数据: %s%s", f.Request.URL.Host, f.Request.URL.Path)

		// 修改响应
		f.Response.StatusCode = 200
		f.Response.Body = []byte(i.config.FakeResponse)
		f.Response.Header.Set("Content-Type", "application/json")
		f.Response.Header.Set("Content-Length", string(len(i.config.FakeResponse)))
		f.Response.Header.Set("X-Intercepted-By", "go-augment-intercept-report")

		// 移除拦截标记
		f.Request.Header.Del("X-Intercept")
	}

	if i.config.Debug {
		i.log.Debugf("响应: %d %s", f.Response.StatusCode, f.Request.URL.String())
	}
}

// StreamRequestModifier 流式请求修改器
func (i *Interceptor) StreamRequestModifier(f *proxy.Flow, in io.Reader) io.Reader {
	return in
}

// StreamResponseModifier 流式响应修改器
func (i *Interceptor) StreamResponseModifier(f *proxy.Flow, in io.Reader) io.Reader {
	return in
}

// isTargetDomain 检查是否为目标域名
func (i *Interceptor) isTargetDomain(host string) bool {
	if len(i.config.Domains) == 0 {
		return true // 如果没有配置域名，则拦截所有
	}

	for _, domain := range i.config.Domains {
		if host == domain || strings.HasSuffix(host, "."+domain) {
			return true
		}
	}
	return false
}

// shouldIntercept 检查是否应该拦截该路径
func (i *Interceptor) shouldIntercept(path string) bool {
	for _, interceptPath := range i.config.InterceptPaths {
		if strings.HasPrefix(path, interceptPath) {
			return true
		}
	}
	return false
}
