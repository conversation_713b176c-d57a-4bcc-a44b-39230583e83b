package logger

import (
	"os"

	"github.com/sirupsen/logrus"
)

var log *logrus.Logger

// Init 初始化日志器
func Init(debug bool) {
	log = logrus.New()
	
	// 设置输出
	log.SetOutput(os.Stdout)
	
	// 设置日志级别
	if debug {
		log.SetLevel(logrus.DebugLevel)
	} else {
		log.SetLevel(logrus.InfoLevel)
	}
	
	// 设置日志格式
	log.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
		ForceColors:   true,
	})
}

// GetLogger 获取日志器实例
func GetLogger() *logrus.Logger {
	if log == nil {
		Init(false) // 默认非调试模式
	}
	return log
}
