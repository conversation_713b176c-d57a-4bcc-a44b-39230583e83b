#!/bin/bash

# macOS/Linux启动脚本

echo "启动 Go Augment Intercept Report 代理系统..."

# 检查是否有sudo权限
if [ "$EUID" -ne 0 ]; then
    echo "警告: 未检测到root权限，可能无法修改hosts文件"
    echo "请使用 sudo 运行此脚本"
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
else
    echo "检测到root权限，可以修改hosts文件"
fi

# 设置默认配置
DOMAINS="1.a.com,2.a.com,3.a.com,4.a.com,5.a.com"
INTERCEPT_PATHS="/client-metrics"
FAKE_RESPONSE="{}"

# 启动代理
echo "启动代理服务器..."
echo "代理地址: http://127.0.0.1:9080"
echo "Web界面: http://127.0.0.1:9081"
echo "拦截域名: $DOMAINS"
echo "拦截路径: $INTERCEPT_PATHS"
echo

../build/go-augment-intercept-report -d "$DOMAINS" -p "$INTERCEPT_PATHS" -r "$FAKE_RESPONSE" --debug
