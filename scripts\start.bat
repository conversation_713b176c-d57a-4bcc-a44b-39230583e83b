@echo off
REM Windows启动脚本

echo 启动 Go Augment Intercept Report 代理系统...

REM 检查是否以管理员权限运行
net session >nul 2>&1
if %errorLevel% == 0 (
    echo 检测到管理员权限，可以修改hosts文件
) else (
    echo 警告: 未检测到管理员权限，可能无法修改hosts文件
    echo 请以管理员身份运行此脚本
    pause
    exit /b 1
)

REM 设置默认配置
set DOMAINS=1.a.com,2.a.com,3.a.com,4.a.com,5.a.com
set INTERCEPT_PATHS=/client-metrics
set FAKE_RESPONSE={}

REM 启动代理
echo 启动代理服务器...
echo 代理地址: http://127.0.0.1:9080
echo Web界面: http://127.0.0.1:9081
echo 拦截域名: %DOMAINS%
echo 拦截路径: %INTERCEPT_PATHS%
echo.

..\build\go-augment-intercept-report.exe -d "%DOMAINS%" -p "%INTERCEPT_PATHS%" -r "%FAKE_RESPONSE%" --debug

pause
